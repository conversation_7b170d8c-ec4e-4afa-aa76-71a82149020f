import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.animation import FuncAnimation
import imageio

# 1. 定义利萨茹曲线的参数
a = 4

b = 3
c = 2
delta_1 = np.pi / 2
delta_2 = np.pi / 4

# 2. 创建一个时间数组
t = np.linspace(0, 2 * np.pi, 300)

# 3. 计算利萨茹曲线的坐标
x = np.sin(a * t + delta_1)
y = np.sin(b * t + delta_2)
z = np.sin(c * t)

# 4. 设置3D图形
fig = plt.figure(figsize=(8, 8))
ax = fig.add_subplot(111, projection='3d')

# 设置坐标轴范围
ax.set_xlim([-1.2, 1.2])
ax.set_ylim([-1.2, 1.2])
ax.set_zlim([-1.2, 1.2])

# 隐藏坐标轴
ax.set_axis_off()

# 绘制完整的利萨茹曲线路径（灰色作为背景）
ax.plot(x, y, z, color='gray', alpha=0.5)

# 初始化无人机（一个点）和它走过的路径
drone, = ax.plot([], [], [], 'ro', markersize=10) # "无人机"是一个红点
path, = ax.plot([], [], [], color='red', linewidth=2) # 无人机飞过的路径

# 5. 创建动画更新函数
def update(frame):
    # 更新无人机的位置
    drone.set_data([x[frame]], [y[frame]])
    drone.set_3d_properties([z[frame]])

    # 更新无人机飞过的路径
    path.set_data(x[:frame+1], y[:frame+1])
    path.set_3d_properties(z[:frame+1])

    return drone, path

# 6. 创建动画
ani = FuncAnimation(fig, update, frames=len(t), blit=True, interval=25)

# 7. 将动画保存为GIF
# 注意：您可能需要安装 imageio 和 pillow
# pip install imageio pillow
ani.save('drone_lissajous.gif', writer='pillow', fps=30)

plt.show()