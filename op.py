import matplotlib.pyplot as plt

# 数据
features = ['多层柔性电路', '解刨学锚点设计']
highlights = ['可拉伸基底', '气孔透气设计']
limitations = ['厚度>2mm影响触觉', '需每日校准']

# 设置图形大小
plt.figure(figsize=(10, 6))

# 绘制条形图
plt.barh(features, [1, 1], color='skyblue', label='结构特征')
plt.barh(highlights, [2, 2], left=[1, 1], color='lightgreen', label='技术亮点')
plt.barh(limitations, [3, 3], left=[3, 3], color='salmon', label='局限性')

# 添加标签和标题
plt.yticks([0, 1, 3])
plt.gca().set_yticklabels(['结构特征', '技术亮点', '局限性'])
plt.xlabel('特征')
plt.title('手套设计特征分析')
plt.legend()

# 显示图形
plt.show()