import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.animation import FuncAnimation
import matplotlib.patches as patches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 1. 定义8字轨迹参数
A = 2.0  # 8字的宽度
B = 1.0  # 8字的高度
height = 5.0  # 飞行高度
speed = 1.0  # 飞行速度

# 2. 时间参数
duration = 20.0  # 飞行持续时间(秒)
dt = 0.1  # 时间步长
t = np.arange(0, duration, dt)

# 3. 计算8字轨迹的x,y,z坐标
# 使用参数方程生成水平8字轨迹
# x = A * sin(t)
# y = B * sin(2*t)
# z = 固定高度
omega = 2 * np.pi / 10.0  # 角频率，10秒完成一个8字

x = A * np.sin(omega * t)
y = B * np.sin(2 * omega * t)
z = np.full_like(t, height)  # 保持固定高度

# 4. 计算偏航角(yaw) - 基于飞行方向
dx = np.gradient(x)
dy = np.gradient(y)
yaw = np.arctan2(dy, dx)  # 偏航角(弧度)
yaw_deg = np.degrees(yaw)  # 转换为度数

# 5. 创建3D图形和GUI
fig = plt.figure(figsize=(15, 10))

# 创建子图
ax1 = fig.add_subplot(221, projection='3d')  # 3D轨迹
ax2 = fig.add_subplot(222)  # XY平面视图
ax3 = fig.add_subplot(223)  # 高度时间图
ax4 = fig.add_subplot(224)  # 偏航角时间图

# 设置3D轨迹图
ax1.set_xlim(-3, 3)
ax1.set_ylim(-2, 2)
ax1.set_zlim(0, 8)
ax1.set_xlabel('X (m)')
ax1.set_ylabel('Y (m)')
ax1.set_zlabel('Z (m)')
ax1.set_title('无人机3D飞行轨迹')

# 绘制完整的8字轨迹路径
ax1.plot(x, y, z, 'b--', alpha=0.5, linewidth=1, label='预定轨迹')

# 设置XY平面视图
ax2.set_xlim(-3, 3)
ax2.set_ylim(-2, 2)
ax2.set_xlabel('X (m)')
ax2.set_ylabel('Y (m)')
ax2.set_title('水平8字轨迹')
ax2.plot(x, y, 'b--', alpha=0.5, linewidth=1)
ax2.grid(True)
ax2.set_aspect('equal')

# 设置高度时间图
ax3.set_xlim(0, duration)
ax3.set_ylim(0, 8)
ax3.set_xlabel('时间 (s)')
ax3.set_ylabel('高度 (m)')
ax3.set_title('高度-时间曲线')
ax3.plot(t, z, 'g-', linewidth=2)
ax3.grid(True)

# 设置偏航角时间图
ax4.set_xlim(0, duration)
ax4.set_ylim(-180, 180)
ax4.set_xlabel('时间 (s)')
ax4.set_ylabel('偏航角 (度)')
ax4.set_title('偏航角-时间曲线')
ax4.plot(t, yaw_deg, 'r-', linewidth=2)
ax4.grid(True)

# 初始化动画元素
drone_3d, = ax1.plot([], [], [], 'ro', markersize=8, label='无人机')
path_3d, = ax1.plot([], [], [], 'r-', linewidth=3, alpha=0.8, label='飞行路径')

drone_xy, = ax2.plot([], [], 'ro', markersize=8)
path_xy, = ax2.plot([], [], 'r-', linewidth=2)

height_point, = ax3.plot([], [], 'go', markersize=8)
yaw_point, = ax4.plot([], [], 'ro', markersize=8)

# 添加无人机方向指示器
arrow_xy = patches.FancyArrowPatch((0, 0), (0, 0), 
                                   connectionstyle="arc3", 
                                   arrowstyle='->', 
                                   mutation_scale=20, 
                                   color='red')
ax2.add_patch(arrow_xy)

# 添加图例
ax1.legend()

# 添加实时数据显示
info_text = fig.text(0.02, 0.95, '', fontsize=10, verticalalignment='top')

def update(frame):
    if frame >= len(t):
        return (drone_3d, path_3d, drone_xy, path_xy, arrow_xy, 
               height_point, yaw_point, info_text)
    
    current_time = t[frame]
    current_x = x[frame]
    current_y = y[frame] 
    current_z = z[frame]
    current_yaw = yaw_deg[frame]
    
    # 更新3D视图
    drone_3d.set_data([current_x], [current_y])
    drone_3d.set_3d_properties([current_z])
    
    # 显示飞行路径
    path_3d.set_data(x[:frame+1], y[:frame+1])
    path_3d.set_3d_properties(z[:frame+1])
    
    # 更新XY平面视图
    drone_xy.set_data([current_x], [current_y])
    path_xy.set_data(x[:frame+1], y[:frame+1])
    
    # 更新方向箭头
    arrow_length = 0.3
    end_x = current_x + arrow_length * np.cos(yaw[frame])
    end_y = current_y + arrow_length * np.sin(yaw[frame])
    arrow_xy.set_positions((float(current_x), float(current_y)), (float(end_x), float(end_y)))
    
    # 更新高度点
    height_point.set_data([current_time], [current_z])
    
    # 更新偏航角点
    yaw_point.set_data([current_time], [current_yaw])
    
    # 更新信息显示
    info_text.set_text(f'时间: {current_time:.1f}s\n'
                      f'位置: X={current_x:.2f}m, Y={current_y:.2f}m, Z={current_z:.2f}m\n'
                      f'偏航角: {current_yaw:.1f}°\n'
                      f'速度: {np.sqrt(dx[frame]**2 + dy[frame]**2):.2f}m/s')
    
    return (drone_3d, path_3d, drone_xy, path_xy, arrow_xy, 
           height_point, yaw_point, info_text)

# 创建动画
ani = FuncAnimation(fig, update, frames=len(t), interval=100, blit=False, repeat=True)

# 调整布局
plt.tight_layout()

# 保存动画为GIF
print("正在生成8字飞行动画...")
ani.save('figure8_drone.gif', writer='pillow', fps=10)
print("动画已保存为 figure8_drone.gif")

# 显示图形
plt.show()

# 输出轨迹数据统计
print(f"\n=== 飞行轨迹统计 ===")
print(f"总飞行时间: {duration:.1f}秒")
print(f"轨迹点数: {len(t)}")
print(f"X坐标范围: {x.min():.2f}m 到 {x.max():.2f}m")
print(f"Y坐标范围: {y.min():.2f}m 到 {y.max():.2f}m") 
print(f"飞行高度: {height:.1f}m")
print(f"偏航角范围: {yaw_deg.min():.1f}° 到 {yaw_deg.max():.1f}°")
print(f"平均飞行速度: {np.mean(np.sqrt(dx**2 + dy**2)):.2f}m/s")