import matplotlib.pyplot as plt
import numpy as np

# 时间点（例如：0到1000小时）
time_hours = np.linspace(0, 1000, 100)

# 假设的性能衰减曲线，这里我们使用一个简化的模型
# 假设精度从0.5°开始，每100小时下降0.001°
precision_loss = 0.5 - 0.001 * time_hours

# 创建图形
plt.figure(figsize=(10, 6))
plt.plot(time_hours, precision_loss, label='精度衰减曲线', color='red')

# 添加标题和标签
plt.title('高端动作捕捉手套性能衰减曲线')
plt.xlabel('使用时间 (小时)')
plt.ylabel('关节角度精度 (°)')
plt.legend()

# 显示网格
plt.grid(True)

# 显示图形
plt.show()