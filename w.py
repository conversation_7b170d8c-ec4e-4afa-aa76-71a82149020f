import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

# 数据 - 按数值大小从高到低排序
categories = ['Surface Resistance', 'Abrasion Resistance', 'Tensile Strength', 'Air Permeability']
values_1 = [120, 50, 45, 0.8]  # 按对应顺序排列
values_2 = [85, 25, 32, 3.5]   # 按对应顺序排列

# 创建图形和3D轴 - 增大图形尺寸
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')

# 设置条形图的位置
x = np.arange(len(categories))
y = np.zeros(len(categories))
z = np.zeros(len(categories))

# 绘制条形图
ax.bar3d(x, y, z, dx=0.5, dy=0.5, dz=values_1, color='b', alpha=0.7, label='Polyester + PU Coating')
ax.bar3d(x, y+0.5, z, dx=0.5, dy=0.5, dz=values_2, color='r', alpha=0.7, label='Coolmax® Knit')

# 设置轴标签
ax.set_xticks(x)
ax.set_xticklabels(categories, rotation=45, ha='right', fontsize=10)
ax.set_ylabel('Materials', fontsize=12, fontweight='bold')
ax.set_zlabel('Values', fontsize=12, fontweight='bold')

# 添加图例
ax.legend()

# 保存图形
plt.savefig('material_comparison_w.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
print("Chart saved as: material_comparison_w.png")

# 显示图形
plt.show()