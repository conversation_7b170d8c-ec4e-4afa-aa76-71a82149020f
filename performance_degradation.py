import matplotlib.pyplot as plt
import numpy as np
import matplotlib
font_list = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'DejaVu Sans']
matplotlib.rcParams['font.sans-serif'] = font_list
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# 设置matplotlib
matplotlib.use('TkAgg')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12

# 模拟数据：高端动作捕捉手套性能衰减
# 使用时长（月）
months = np.linspace(0, 24, 50)

# 不同性能指标的衰减曲线（从100%开始衰减）
# 关节角度精度（指数衰减）
angle_precision = 100 * np.exp(-0.03 * months)

# IMU传感器响应时间（线性增加，表示延迟增加）
response_delay = 1 + 0.2 * months

# 电池续航能力（阶段性衰减）
battery_life = 100 * (0.98 ** months)

# 防水性能（台阶函数衰减）
waterproof = np.where(months < 6, 100, 
              np.where(months < 12, 85,
              np.where(months < 18, 70, 50)))

# 整体耐用性评分（综合衰减）
durability = 100 * (0.97 ** months) * (1 - 0.002 * months**1.5)

# 创建图形
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
fig.suptitle('Smart Gloves - Performance Degradation Analysis', 
             fontsize=20, fontweight='bold', y=0.98)

# 第一个子图：精度相关指标
ax1.plot(months, angle_precision, 'b-', linewidth=3, label='Joint Angle Precision (%)', marker='o', markersize=4)
ax1.plot(months, 100-response_delay*10, 'r--', linewidth=2.5, label='Response Speed (%)', marker='s', markersize=3)
ax1.plot(months, battery_life, 'g:', linewidth=2.5, label='Battery Life (%)', marker='^', markersize=4)

ax1.set_xlabel('Usage Duration (Months)', fontsize=14, fontweight='bold')
ax1.set_ylabel('Performance (%)', fontsize=14, fontweight='bold')
ax1.set_title('Precision & Response Performance Degradation', fontsize=18, fontweight='bold', pad=15)
ax1.grid(True, alpha=0.3)
ax1.legend(loc='upper right', fontsize=12)
ax1.set_xlim(0, 24)
ax1.set_ylim(0, 105)

# 添加性能阶段标注
ax1.axvspan(0, 6, alpha=0.1, color='green')
ax1.axvspan(6, 12, alpha=0.1, color='yellow')
ax1.axvspan(12, 18, alpha=0.1, color='orange')
ax1.axvspan(18, 24, alpha=0.1, color='red')

# 第二个子图：耐用性指标
ax2.plot(months, waterproof, 'purple', linewidth=3, label='Waterproof Rating (%)', marker='D', markersize=4)
ax2.plot(months, durability, 'orange', linewidth=2.5, label='Overall Durability (%)', marker='*', markersize=5)

# 添加成本效益线（虚线）
cost_effectiveness = 100 - 2.5 * months
ax2.plot(months, np.maximum(cost_effectiveness, 0), 'gray', linestyle='-.', linewidth=2, 
         label='Cost Effectiveness (%)', alpha=0.8)

ax2.set_xlabel('Usage Duration (Months)', fontsize=14, fontweight='bold')
ax2.set_ylabel('Performance (%)', fontsize=14, fontweight='bold')
ax2.set_title('Durability & Cost-Effectiveness Analysis', fontsize=18, fontweight='bold', pad=15)
ax2.grid(True, alpha=0.3)
ax2.legend(loc='upper right', fontsize=12)
ax2.set_xlim(0, 24)
ax2.set_ylim(0, 105)

# 添加使用阶段背景色
ax2.axvspan(0, 6, alpha=0.1, color='green')
ax2.axvspan(6, 12, alpha=0.1, color='yellow')
ax2.axvspan(12, 18, alpha=0.1, color='orange')
ax2.axvspan(18, 24, alpha=0.1, color='red')

# 调整布局
plt.tight_layout()

# 保存图形
plt.savefig('motion_capture_gloves_degradation.png', dpi=600, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
print("Performance degradation chart saved as: motion_capture_gloves_degradation.png")

# 显示图形
plt.show()