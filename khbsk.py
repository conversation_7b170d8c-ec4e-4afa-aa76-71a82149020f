import numpy as np
import matplotlib.pyplot as plt

# --- 1. 数据定义 ---
# 维度标签
labels = np.array(['成本效益', '耐用性', '环境适应性', '应用精度', '续航能力', '维护便捷性'])
n_dims = len(labels)

# 各方案的评分数据
your_product_data = np.array([5, 5, 5, 3, 5, 5])
mocap_glove_data = np.array([1, 2, 1, 5, 2, 1])
medical_glove_data = np.array([2, 2, 2, 5, 3, 1])
robotic_glove_data = np.array([3, 3, 3, 4, 3, 3])

# 为了让雷达图闭合，需要将第一个点的数据追加到最后
your_product_data = np.concatenate((your_product_data, [your_product_data[0]]))
mocap_glove_data = np.concatenate((mocap_glove_data, [mocap_glove_data[0]]))
medical_glove_data = np.concatenate((medical_glove_data, [medical_glove_data[0]]))
robotic_glove_data = np.concatenate((robotic_glove_data, [robotic_glove_data[0]]))

# --- 2. 绘图设置 ---
# 设置中文字体，请根据您的操作系统选择合适的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows/Linux用户
# plt.rcParams['font.sans-serif'] = ['PingFang SC'] # Mac用户
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 计算每个维度的角度
angles = np.linspace(0, 2 * np.pi, n_dims, endpoint=False)
# 为了闭合，同样追加第一个角度
angles = np.concatenate((angles, [angles[0]]))

# 创建画布和极坐标系
fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))

# --- 3. 绘制雷达图 ---
# 绘制您的方案
ax.plot(angles, your_product_data, 'o-', color='dodgerblue', linewidth=2, label='您的方案 (工业监测)')
ax.fill(angles, your_product_data, alpha=0.25, color='dodgerblue')

# 绘制竞品1
ax.plot(angles, mocap_glove_data, 'o-', color='red', linewidth=1, label='竞品1 (高保真动捕)')
ax.fill(angles, mocap_glove_data, alpha=0.1, color='red')

# 绘制竞品2
ax.plot(angles, medical_glove_data, 'o-', color='green', linewidth=1, label='竞品2 (精细压力)')
ax.fill(angles, medical_glove_data, alpha=0.1, color='green')

# 绘制竞品3
ax.plot(angles, robotic_glove_data, 'o-', color='purple', linewidth=1, label='竞品3 (机械遥操作)')
ax.fill(angles, robotic_glove_data, alpha=0.1, color='purple')

# --- 4. 美化图表 ---
# 设置图表标题
ax.set_title('智能手套解决方案六维度竞争力分析雷达图', fontsize=20, pad=20)

# 设置维度标签
ax.set_thetagrids(angles[:-1] * 180 / np.pi, labels, fontsize=14)

# 设置评分范围及网格
ax.set_ylim(0, 5.5)
ax.set_rgrids(np.arange(1, 6), labels=['1', '2', '3', '4', '5分'], angle=0, fontsize=12)
ax.set_rlabel_position(0) # 将评分标签放在0度角位置

# 添加图例
plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1), fontsize=12)

# 显示图表
plt.tight_layout()
plt.show()