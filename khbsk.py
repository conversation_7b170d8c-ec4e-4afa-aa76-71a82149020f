import numpy as np
import matplotlib
import matplotlib.pyplot as plt
font_list = ['WenQuanYi Micro Hei', 'SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'DejaVu Sans']
matplotlib.rcParams['font.sans-serif'] = font_list
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# --- 1. 数据定义 ---
# Dimension labels
labels = np.array(['Cost Effectiveness', 'Durability', 'Environmental Adaptability', 'Application Precision', 'Battery Life', 'Maintenance Convenience'])
n_dims = len(labels)

# Scoring data for each solution
your_product_data = np.array([5, 5, 5, 3, 5, 5])
mocap_glove_data = np.array([1, 2, 1, 5, 2, 1])
medical_glove_data = np.array([2, 2, 2, 5, 3, 1])
robotic_glove_data = np.array([3, 3, 3, 4, 3, 3])

# To close the radar chart, append the first data point to the end
your_product_data = np.concatenate((your_product_data, [your_product_data[0]]))
mocap_glove_data = np.concatenate((mocap_glove_data, [mocap_glove_data[0]]))
medical_glove_data = np.concatenate((medical_glove_data, [medical_glove_data[0]]))
robotic_glove_data = np.concatenate((robotic_glove_data, [robotic_glove_data[0]]))

# --- 2. Plot settings ---
# Set font for English display
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False  # Fix minus sign display issue

# Calculate angle for each dimension
angles = np.linspace(0, 2 * np.pi, n_dims, endpoint=False)
# To close the chart, append the first angle as well
angles = np.concatenate((angles, [angles[0]]))

# Create canvas and polar coordinate system
fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))

# --- 3. Draw radar chart ---
# Draw your solution
ax.plot(angles, your_product_data, 'o-', color='dodgerblue', linewidth=2, label='My Products (Industrial Inspection Gloves)')
ax.fill(angles, your_product_data, alpha=0.25, color='dodgerblue')

# Draw competitor 1
ax.plot(angles, mocap_glove_data, 'o-', color='red', linewidth=1, label='Competitor 1 (High-end Mocap Gloves)')
ax.fill(angles, mocap_glove_data, alpha=0.1, color='red')

# Draw competitor 2
ax.plot(angles, medical_glove_data, 'o-', color='green', linewidth=1, label='Competitor 2 (Hand Rehabilitation Glove)')
ax.fill(angles, medical_glove_data, alpha=0.1, color='green')

# Draw competitor 3
ax.plot(angles, robotic_glove_data, 'o-', color='purple', linewidth=1, label='Competitor 3 (Robotic Teleoperation Glove)')
ax.fill(angles, robotic_glove_data, alpha=0.1, color='purple')

# --- 4. Chart beautification ---
# Set chart title
ax.set_title('Smart Glove  - Six-Dimensional Competitiveness Analysis Radar Chart', fontsize=16, pad=20)

# Set dimension labels
ax.set_thetagrids(angles[:-1] * 180 / np.pi, labels, fontsize=14)

# Set score range and grid
ax.set_ylim(0, 5.5)
ax.set_rgrids(np.arange(1, 6), labels=['1', '2', '3', '4', '5 pts'], angle=0, fontsize=12)
ax.set_rlabel_position(0)  # Place score labels at 0 degree angle

# Add legend
plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1), fontsize=12)

# Save chart
plt.tight_layout()
plt.savefig('smart_glove_competitiveness_analysis_radar.png', dpi=300, bbox_inches='tight')
print("Radar chart saved as 'smart_glove_competitiveness_analysis_radar.png'")
plt.show()  # Comment out display function in server environment