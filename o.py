import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import matplotlib

# 设置matplotlib - 使用TkAgg后端支持显示
matplotlib.use('TkAgg')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
plt.rcParams['font.size'] = 10

# 数据 - 使用英文标签
parameters = ['d33', 'Epsilon', 'Young\'s Modulus', 'Elongation', 'Curie Temp', 'Density', 'Fatigue Life']
values = np.array([
    [23, 12, 2.5, 50, 170, 1.78, 100000],
    [593, 3400, 63, 0.3, 193, 7.5, 10000],
    [18, 9, 1.8, 120, -1, 1.2, 500000]
])
materials = ['PVDF\n(Kureha KF-1-100)', 'PZT-5H\n(APC 841)', 'Graphene Composite\n(Graphene Supermarket)']

# 创建图形 - 增大尺寸
fig = plt.figure(figsize=(18, 14))
ax = fig.add_subplot(111, projection='3d')

# 设置颜色方案
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3', '#54A0FF']
material_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

# 创建3D条形图 - 减小条形图尺寸增加间距
bar_width = 0.15
bar_depth = 0.4

for i, material in enumerate(materials):
    x_pos = np.arange(len(parameters))
    y_pos = np.full(len(parameters), i)
    z_pos = np.zeros(len(parameters))
    
    # 对数据进行归一化处理以便更好地显示
    normalized_values = np.log10(values[i] + 1)  # 使用对数归一化
    
    ax.bar3d(x_pos, y_pos, z_pos, bar_width, bar_depth, normalized_values,
             color=material_colors[i], alpha=0.8, label=materials[i])

# 美化图形 - 增大轴标签字体和间距
ax.set_xlabel('Parameter Type', fontsize=14, fontweight='bold', labelpad=15)
ax.set_ylabel('Material Type', fontsize=14, fontweight='bold', labelpad=15)
ax.set_zlabel('Value (Log Scale)', fontsize=14, fontweight='bold', labelpad=15)

# 设置坐标轴刻度 - 隐藏X轴标签
ax.set_xticks(np.arange(len(parameters)))
ax.set_xticklabels([])  # 隐藏X轴标签
ax.set_yticks(np.arange(len(materials)))
ax.set_yticklabels([])

# 增加坐标轴标签的间距
ax.tick_params(axis='x', pad=10)
ax.tick_params(axis='y', pad=8)
ax.tick_params(axis='z', pad=5)

# 设置标题 - 增大字体
ax.set_title('Material Performance Parameter Comparison\n(3D Bar Chart)', fontsize=20, fontweight='bold', pad=30)

# 设置图例 - 增大字体并调整位置
ax.legend(loc='upper left', bbox_to_anchor=(0.02, 0.98), fontsize=12)

# 设置视角
ax.view_init(elev=20, azim=45)

# 设置网格
ax.grid(True, alpha=0.3)

# 调整布局 - 增加更大边距避免文字重叠
plt.subplots_adjust(left=0.15, right=0.85, top=0.85, bottom=0.2)

# 保存图形
plt.savefig('material_comparison_chart.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
print("Chart saved as: material_comparison_chart.png")

# 显示图形
plt.show()